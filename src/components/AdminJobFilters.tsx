import { Search, Filter, X, Calendar, Building, MapPin, Tag, RotateCcw } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { AdminJobFilters as AdminJobFiltersType, AdminSortOption } from '@/types/job';

interface AdminJobFiltersProps {
  filters: AdminJobFiltersType;
  onFiltersChange: (filters: Partial<AdminJobFiltersType>) => void;
  onClearFilters: () => void;
  sortBy: AdminSortOption;
  onSortChange: (sort: AdminSortOption) => void;
  resultsCount: number;
  hasActiveFilters: boolean;
  filterOptions: {
    companies: string[];
    locations: string[];
    categories: string[];
  };
  isLoading?: boolean;
}

export const AdminJobFilters = ({
  filters,
  onFiltersChange,
  onClearFilters,
  sortBy,
  onSortChange,
  resultsCount,
  hasActiveFilters,
  filterOptions,
  isLoading = false
}: AdminJobFiltersProps) => {
  
  const updateFilter = (key: keyof AdminJobFiltersType, value: string) => {
    onFiltersChange({ [key]: value });
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.search) count++;
    if (filters.status !== 'all') count++;
    if (filters.company) count++;
    if (filters.location) count++;
    if (filters.category) count++;
    if (filters.dateFrom) count++;
    if (filters.dateTo) count++;
    return count;
  };

  const getSortLabel = (sort: AdminSortOption) => {
    const labels: Record<AdminSortOption, string> = {
      'newest': 'Mais Recentes',
      'oldest': 'Mais Antigas',
      'title-asc': 'Título A-Z',
      'title-desc': 'Título Z-A',
      'company-asc': 'Empresa A-Z',
      'company-desc': 'Empresa Z-A'
    };
    return labels[sort];
  };

  const getStatusLabel = (status: string) => {
    const labels: Record<string, string> = {
      'all': 'Todos os Status',
      'active': 'Apenas Ativas',
      'inactive': 'Apenas Inativas'
    };
    return labels[status] || status;
  };

  return (
    <Card className="p-6 bg-gradient-card backdrop-blur-sm border-border/50">
      {/* Search Bar */}
      <div className="relative mb-6">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
        <Input
          placeholder="Buscar vagas por título, descrição, empresa..."
          value={filters.search}
          onChange={(e) => updateFilter('search', e.target.value)}
          className="pl-10 h-12 text-base"
          disabled={isLoading}
        />
      </div>

      {/* Filter Controls */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 mb-6">
        {/* Status Filter */}
        <div className="space-y-2">
          <Label className="text-sm font-medium flex items-center gap-2">
            <Tag className="w-4 h-4" />
            Status
          </Label>
          <Select 
            value={filters.status} 
            onValueChange={(value) => updateFilter('status', value)}
            disabled={isLoading}
          >
            <SelectTrigger className="h-10">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Todos os Status</SelectItem>
              <SelectItem value="active">Apenas Ativas</SelectItem>
              <SelectItem value="inactive">Apenas Inativas</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Company Filter */}
        <div className="space-y-2">
          <Label className="text-sm font-medium flex items-center gap-2">
            <Building className="w-4 h-4" />
            Empresa
          </Label>
          <Select 
            value={filters.company} 
            onValueChange={(value) => updateFilter('company', value)}
            disabled={isLoading}
          >
            <SelectTrigger className="h-10">
              <SelectValue placeholder="Todas as empresas" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">Todas as empresas</SelectItem>
              {filterOptions.companies.map((company) => (
                <SelectItem key={company} value={company}>
                  {company}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Location Filter */}
        <div className="space-y-2">
          <Label className="text-sm font-medium flex items-center gap-2">
            <MapPin className="w-4 h-4" />
            Localização
          </Label>
          <Select 
            value={filters.location} 
            onValueChange={(value) => updateFilter('location', value)}
            disabled={isLoading}
          >
            <SelectTrigger className="h-10">
              <SelectValue placeholder="Todas as localizações" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">Todas as localizações</SelectItem>
              {filterOptions.locations.map((location) => (
                <SelectItem key={location} value={location}>
                  {location}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Category Filter */}
        <div className="space-y-2">
          <Label className="text-sm font-medium flex items-center gap-2">
            <Filter className="w-4 h-4" />
            Categoria
          </Label>
          <Select 
            value={filters.category} 
            onValueChange={(value) => updateFilter('category', value)}
            disabled={isLoading}
          >
            <SelectTrigger className="h-10">
              <SelectValue placeholder="Todas as categorias" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">Todas as categorias</SelectItem>
              {filterOptions.categories.map((category) => (
                <SelectItem key={category} value={category}>
                  {category}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Date Range Filters */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
        <div className="space-y-2">
          <Label className="text-sm font-medium flex items-center gap-2">
            <Calendar className="w-4 h-4" />
            Data Inicial
          </Label>
          <Input
            type="date"
            value={filters.dateFrom}
            onChange={(e) => updateFilter('dateFrom', e.target.value)}
            className="h-10"
            disabled={isLoading}
          />
        </div>
        <div className="space-y-2">
          <Label className="text-sm font-medium flex items-center gap-2">
            <Calendar className="w-4 h-4" />
            Data Final
          </Label>
          <Input
            type="date"
            value={filters.dateTo}
            onChange={(e) => updateFilter('dateTo', e.target.value)}
            className="h-10"
            disabled={isLoading}
          />
        </div>
      </div>

      {/* Sort and Results Info */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div className="flex items-center gap-4">
          {/* Results Count */}
          <span className="text-sm font-medium text-foreground">
            {isLoading ? 'Carregando...' : `${resultsCount} ${resultsCount === 1 ? 'vaga encontrada' : 'vagas encontradas'}`}
          </span>
          
          {/* Active Filters Badge */}
          {hasActiveFilters && (
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="text-xs">
                <Filter className="w-3 h-3 mr-1" />
                {getActiveFiltersCount()} {getActiveFiltersCount() === 1 ? 'filtro ativo' : 'filtros ativos'}
              </Badge>
              <Button
                variant="ghost"
                size="sm"
                onClick={onClearFilters}
                className="h-6 px-2 text-xs hover:bg-destructive/10 hover:text-destructive"
                disabled={isLoading}
              >
                <X className="w-3 h-3 mr-1" />
                Limpar
              </Button>
            </div>
          )}
        </div>

        {/* Sort Selector */}
        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2">
          <Label className="text-sm font-medium whitespace-nowrap">Ordenar por:</Label>
          <Select value={sortBy} onValueChange={onSortChange} disabled={isLoading}>
            <SelectTrigger className="w-full sm:w-40 h-8">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="newest">Mais Recentes</SelectItem>
              <SelectItem value="oldest">Mais Antigas</SelectItem>
              <SelectItem value="title-asc">Título A-Z</SelectItem>
              <SelectItem value="title-desc">Título Z-A</SelectItem>
              <SelectItem value="company-asc">Empresa A-Z</SelectItem>
              <SelectItem value="company-desc">Empresa Z-A</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </Card>
  );
};
