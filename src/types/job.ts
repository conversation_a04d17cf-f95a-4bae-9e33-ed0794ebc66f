export interface Job {
  id: string;
  title: string;
  company: string;
  description: string;
  requirements: string | string[];
  location: string;
  salary?: string;
  contractType: 'full-time' | 'part-time' | 'contract' | 'freelance' | 'internship';
  experienceLevel: 'entry' | 'mid' | 'senior' | 'executive';
  category: string;
  isUrgent?: boolean;
  isFeatured?: boolean;
  postedDate: Date;
  applicationUrl?: string;
  companyLogo?: string;
  source?: 'manual' | 'hardfranca';
  isActive?: boolean;
  // Alternative property names for backward compatibility
  type?: string;
  experience?: string;
  postedAt?: string;
}

export interface JobFilters {
  search: string;
  category: string;
  location: string;
  contractType: string;
  experienceLevel: string;
  salaryMin?: number;
  salaryMax?: number;
}

export type SortOption = 'newest' | 'oldest' | 'relevance' | 'salary-high' | 'salary-low';

// Admin-specific types for job management
export interface AdminJobFilters {
  search: string;
  status: 'all' | 'active' | 'inactive';
  company: string;
  location: string;
  category: string;
  dateFrom: string;
  dateTo: string;
}

export interface AdminJobPagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

export interface AdminJobsResponse {
  jobs: Job[];
  pagination: AdminJobPagination;
}

export type AdminSortOption = 'newest' | 'oldest' | 'title-asc' | 'title-desc' | 'company-asc' | 'company-desc';